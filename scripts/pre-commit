#!/bin/bash

# Pre-commit hook to run image migration scripts
# This script runs before each commit to migrate images to R2

set -e

# Check if there are any changes in src/content directory
if ! git diff --cached --name-only | grep -q "^src/content/"; then
    echo "ℹ️  No changes in src/content directory. Skipping image migrations."
    exit 0
fi

echo "🔄 Running image migrations to R2..."

# Check if required environment variables are set
if [ -z "$R2_ACCESS_KEY_ID" ] || [ -z "$R2_SECRET_ACCESS_KEY" ] || [ -z "$R2_ENDPOINT_URL" ] || [ -z "$R2_BUCKET_NAME" ]; then
    echo "⚠️  Warning: R2 credentials not configured. Skipping image migrations."
    echo "   Configure R2_ACCESS_KEY_ID, R2_SECRET_ACCESS_KEY, R2_ENDPOINT_URL, and R2_BUCKET_NAME in .env"
    exit 0
fi

# Install Python dependencies if not already installed
if ! python3 -c "import boto3, requests, dotenv" 2>/dev/null; then
    pwd
    echo "📦 Installing Python dependencies..."
    if command -v uv >/dev/null 2>&1; then
        uv pip install -r scripts/requirements.txt
    else
        pip3 install -r scripts/requirements.txt
    fi
fi

# Run the migration scripts
echo "🚀 Starting Giphy migration..."
if python3 scripts/migrate_giphy_to_r2.py; then
    echo "✅ Giphy migration completed successfully"
else
    echo "❌ Giphy migration failed"
    exit 1
fi

echo "🚀 Starting image migration..."
if python3 scripts/migrate_images_to_r2.py; then
    echo "✅ Image migration completed successfully"
else
    echo "❌ Image migration failed"
    exit 1
fi

echo "🚀 Starting D2 diagram migration..."
if python3 scripts/migrate_d2_to_r2.py; then
    echo "✅ D2 diagram migration completed successfully"
else
    echo "❌ D2 diagram migration failed"
    exit 1
fi

# Check if any files were modified by the migrations
if ! git diff --quiet; then
    echo "📝 Migrations updated some files. Adding them to the commit..."
    git add src/content/blog/
fi

echo "✨ Pre-commit hook completed"